<template>
  <view class="login-container">
    <view class="login-header">登录</view>
    <view class="login-form">
      <view class="form-item">
        <view class="form-label">
          <uv-icon name="phone" size="34" color="#666" label="手机号"></uv-icon>
        </view>
        <uv-input 
          v-model="form.mobile" 
          placeholder="请输入手机号"
          type="number"
          maxlength="11"
          border="false"
          :customStyle="inputStyle"
        />
      </view>
      
      <view class="form-item">
        <view class="form-label">
          <uv-icon name="lock" size="34" color="#666" label="密码"></uv-icon>
        </view>
        <uv-input 
          v-model="form.password" 
          placeholder="请输入密码"
          :password="!showPassword"
          border="false"
          :customStyle="inputStyle"
        >
        <template #suffix>
          <uv-icon :name="showPassword ? 'eye-off' : 'eye'" size="34" color="#666" @click="togglePassword"></uv-icon>
        </template>
        </uv-input>

      </view>
      
      <view class="login-actions">
        <uv-button 
          type="primary" 
          :loading="loading"
          :disabled="!canLogin"
          @click="handleLogin"
          :customStyle="buttonStyle"
          :text="loading ? '登录中...' : '登录'"
        >
        </uv-button>
      </view>
    </view>
    
    <view class="login-footer">
      <text class="footer-text">如有问题请联系管理员</text>
    </view>
  </view>
</template>

<script>
// {{ AURA-X: Add - 引入公共工具函数，优化代码结构. Approval: 寸止(ID:1735374000). }}
import utils from '@/common/js/utils.js'

export default {
  data() {
    return {
      form: {
        mobile: '',
        password: ''
      },
      showPassword: false,
      loading: false,
      inputStyle: {
        backgroundColor: '#f8f9fa',
        borderRadius: '12rpx',
        padding: '24rpx 32rpx',
        fontSize: '28rpx'
      },
      buttonStyle: {
        borderRadius: '12rpx',
        height: '88rpx',
        fontSize: '28rpx',
        fontWeight: '500'
      }
    }
  },
  computed: {
    canLogin() {
      // {{ AURA-X: Modify - 使用公共验证函数和常量，提高验证准确性. Approval: 寸止(ID:1735374000). }}
      return utils.validateMobile(this.form.mobile) &&
             this.form.password.length >= utils.getConstant('MIN_PASSWORD_LENGTH')
    }
  },
  methods: {
    togglePassword() {
      this.showPassword = !this.showPassword
    },
    
    async handleLogin() {
      if (!this.canLogin) {
        // {{ AURA-X: Modify - 使用公共错误提示函数. Approval: 寸止(ID:1735374000). }}
        utils.showError('请输入正确的手机号和密码')
        return
      }
      
      this.loading = true
      
      try {
        const result = await uniCloud.callFunction({
          name: 'user-auth',
          data: {
            action: 'login',
            data: {
              mobile: this.form.mobile,
              password: this.form.password
            }
          }
        })
        
        if (result.result.code === 0) {
          // 登录成功，保存token和用户信息
          const { token, userInfo } = result.result.data
          
          // 保存到本地存储
          uni.setStorageSync('token', token)
          uni.setStorageSync('userInfo', userInfo)

          // {{ AURA-X: Modify - 使用公共成功提示函数. Approval: 寸止(ID:1735374000). }}
          utils.showSuccess('登录成功')

          // 跳转到首页
          setTimeout(() => {
            uni.reLaunch({
              url: '/pages/shelf-monitor/index'
            })
          }, 1500)
        } else {
          // {{ AURA-X: Modify - 使用公共错误提示函数. Approval: 寸止(ID:1735374000). }}
          utils.showError(result.result.message || '登录失败')
        }
      } catch (error) {
        console.error('登录失败:', error)
        // {{ AURA-X: Modify - 使用公共错误提示函数. Approval: 寸止(ID:1735374000). }}
        utils.showError('网络错误，请重试')
      } finally {
        this.loading = false
      }
    }
  },
  
  onLoad() {
    // 检查是否已经登录
    const token = uni.getStorageSync('token')
    if (token) {
      uni.reLaunch({
        url: '/pages/shelf-monitor/index'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  height: 100vh;
  background: $primary-gradient;
  position: relative;
}

.login-header {
  text-align: center;
  font-size: $font-size-xxl;
  color: #ffffff;
  padding-top: 50rpx;
  font-weight: $font-weight-semibold;
}

.login-form {
  position: absolute;
  top: 50%;
  left: $spacing-lg;
  right: $spacing-lg;
  transform: translateY(-50%);
  background: $bg-color-container;
  border-radius: $border-radius-xl;
  padding: $spacing-xl $spacing-lg;
  margin-bottom: 40rpx;
  box-shadow: $shadow-lg;
}

.form-item {
  margin-bottom: $spacing-lg;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: $spacing-sm;
}

.login-actions {
  margin-top: $spacing-xl;
}

.login-footer {
  text-align: center;
  position: absolute;
  bottom: 40rpx;
  left: 0;
  right: 0;
}

.footer-text {
  font-size: $font-size-sm;
  color: rgba(255, 255, 255, 0.6);
}
</style>