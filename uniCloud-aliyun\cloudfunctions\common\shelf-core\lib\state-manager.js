'use strict'

/**
 * 平台状态码映射常量
 */
const PLATFORM_STATUS_MAPPING = {
  // 租号玩状态映射
  ZUHAOWAN: {
    0: 0,    // 待租 -> 待租
    1: 1,    // 出租中 -> 出租中
    [-1]: -1 // 下架 -> 下架
  },
  // U号租状态映射
  UHAOZU: {
    GOODS_STATUS: {
      ONLINE: 3,   // 上架状态
      OFFLINE: 4   // 下架状态
    },
    RENT_STATUS: {
      RENTED: 0,   // 出租中
      AVAILABLE: 1 // 待租
    }
  }
}

/**
 * 统一状态管理器
 *
 * 负责处理所有平台状态的统一管理、转换和操作逻辑
 * 主要功能：
 * - 统一状态码定义和管理
 * - 不同平台状态码的转换
 * - 状态相关的业务逻辑判断
 * - 状态统计和分析
 */
class StateManager {
  /**
   * 统一状态定义
   */
  static STATES = {
    AVAILABLE: 0,    // 待租
    RENTED: 1,       // 出租中
    OFFLINE: -1,     // 下架
    OTHER: -2        // 其他
  }

  /**
   * 状态文本映射
   */
  static STATE_TEXTS = {
    [StateManager.STATES.AVAILABLE]: '待租',
    [StateManager.STATES.RENTED]: '出租中',
    [StateManager.STATES.OFFLINE]: '已下架',
    [StateManager.STATES.OTHER]: '其他'
  }

  /**
   * 状态样式类映射
   */
  static STATE_CLASSES = {
    [StateManager.STATES.AVAILABLE]: 'available',
    [StateManager.STATES.RENTED]: 'rented',
    [StateManager.STATES.OFFLINE]: 'offline',
    [StateManager.STATES.OTHER]: 'unknown'
  }

  /**
   * 租号玩状态转换
   *
   * @param {number} ztStatus - 租号玩状态码
   * @returns {number} 统一状态码
   */
  static convertZuhaoWanStatus(ztStatus) {
    // {{ AURA-X: Modify - 使用常量映射简化状态转换逻辑. Approval: 寸止(ID:1735373600). }}
    const statusMapping = PLATFORM_STATUS_MAPPING.ZUHAOWAN

    // 直接从映射表获取状态，如果不存在则返回"其他"状态
    return statusMapping.hasOwnProperty(ztStatus)
      ? statusMapping[ztStatus]
      : StateManager.STATES.OTHER
  }

  /**
   * U号租状态转换
   *
   * @param {number} goodsStatus - 商品状态
   * @param {number} rentStatus - 租赁状态
   * @returns {number} 统一状态码
   */
  static convertUhaoZuStatus(goodsStatus, rentStatus) {
    // {{ AURA-X: Modify - 使用常量定义简化U号租状态转换逻辑. Approval: 寸止(ID:1735373600). }}
    const { GOODS_STATUS, RENT_STATUS } = PLATFORM_STATUS_MAPPING.UHAOZU

    // 下架状态
    if (goodsStatus === GOODS_STATUS.OFFLINE) {
      return StateManager.STATES.OFFLINE
    }

    // 上架状态，根据租赁状态判断
    if (goodsStatus === GOODS_STATUS.ONLINE) {
      return rentStatus === RENT_STATUS.RENTED
        ? StateManager.STATES.RENTED
        : StateManager.STATES.AVAILABLE
    }

    // 其他状态
    return StateManager.STATES.OTHER
  }

  /**
   * 获取状态文本
   * @param {number} state 统一状态码
   * @returns {string} 状态文本
   */
  static getStateText(state) {
    return StateManager.STATE_TEXTS[state] || '其他'
  }

  /**
   * 获取状态样式类
   * @param {number} state 统一状态码
   * @returns {string} 样式类名
   */
  static getStateClass(state) {
    return StateManager.STATE_CLASSES[state] || 'unknown'
  }

  /**
   * 检查状态是否可以上架
   *
   * @param {number} state - 统一状态码
   * @returns {boolean} 是否可以上架
   */
  static canOnShelf(state) {
    // {{ AURA-X: Modify - 使用常量定义提高代码可读性. Approval: 寸止(ID:1735373600). }}
    return state === StateManager.STATES.OFFLINE
  }

  /**
   * 检查状态是否可以下架
   *
   * @param {number} state - 统一状态码
   * @returns {boolean} 是否可以下架
   */
  static canOffShelf(state) {
    // {{ AURA-X: Modify - 使用常量定义提高代码可读性. Approval: 寸止(ID:1735373600). }}
    return state === StateManager.STATES.AVAILABLE
  }

  /**
   * 获取操作按钮文本
   *
   * @param {number} state - 统一状态码
   * @returns {string} 按钮文本
   */
  static getActionButtonText(state) {
    // {{ AURA-X: Modify - 简化条件判断逻辑. Approval: 寸止(ID:1735373600). }}
    if (StateManager.canOffShelf(state)) {
      return '下架'
    }

    if (StateManager.canOnShelf(state)) {
      return '上架'
    }

    return '不可操作'
  }

  /**
   * 获取目标状态
   *
   * @param {number} currentState - 当前状态
   * @returns {number} 目标状态
   * @throws {Error} 当前状态不支持切换操作时抛出错误
   */
  static getTargetState(currentState) {
    // {{ AURA-X: Modify - 使用常量定义和更清晰的错误处理. Approval: 寸止(ID:1735373600). }}
    if (StateManager.canOffShelf(currentState)) {
      return StateManager.STATES.OFFLINE
    }

    if (StateManager.canOnShelf(currentState)) {
      return StateManager.STATES.AVAILABLE
    }

    throw new Error(`当前状态 ${StateManager.getStateText(currentState)} 不支持切换操作`)
  }

  /**
   * 验证状态码是否有效
   * @param {number} state 状态码
   * @returns {boolean} 是否有效
   */
  static isValidState(state) {
    return Object.values(StateManager.STATES).includes(state)
  }

  /**
   * 获取所有状态定义
   * @returns {Object} 状态定义对象
   */
  static getAllStates() {
    return { ...StateManager.STATES }
  }

  /**
   * 获取状态统计信息
   *
   * @param {Array} shelfList - 货架列表
   * @returns {Object} 统计信息对象
   */
  static getStateStats(shelfList) {
    // {{ AURA-X: Modify - 使用常量定义和更高效的统计方法. Approval: 寸止(ID:1735373600). }}
    const stats = {
      total: shelfList.length,
      available: 0,
      rented: 0,
      offline: 0,
      other: 0
    }

    // 使用常量进行状态匹配，提高代码可读性
    shelfList.forEach(shelf => {
      const state = shelf.unified_state

      switch (state) {
        case StateManager.STATES.AVAILABLE:
          stats.available++
          break
        case StateManager.STATES.RENTED:
          stats.rented++
          break
        case StateManager.STATES.OFFLINE:
          stats.offline++
          break
        case StateManager.STATES.OTHER:
        default:
          stats.other++
          break
      }
    })

    return stats
  }

  /**
   * 获取状态分布百分比
   *
   * @param {Array} shelfList - 货架列表
   * @returns {Object} 状态分布百分比
   */
  static getStatePercentages(shelfList) {
    // {{ AURA-X: Add - 新增状态分布百分比统计功能. Approval: 寸止(ID:1735373600). }}
    const stats = StateManager.getStateStats(shelfList)
    const total = stats.total

    if (total === 0) {
      return {
        available: 0,
        rented: 0,
        offline: 0,
        other: 0
      }
    }

    return {
      available: Math.round((stats.available / total) * 100),
      rented: Math.round((stats.rented / total) * 100),
      offline: Math.round((stats.offline / total) * 100),
      other: Math.round((stats.other / total) * 100)
    }
  }
}

module.exports = StateManager
