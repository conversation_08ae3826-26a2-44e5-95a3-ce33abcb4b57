# 开发规范和规则

- testPlatformLogin云函数存在重复数据库更新问题：适配器层和云函数层都会更新数据库，且使用user_id+platform_type条件会误更新多个同类型配置。修复方案：使用配置ID精确更新，移除重复逻辑。
- shelf-monitor云函数修复：定时任务需要获取所有用户的平台配置，不能传入null作为用户ID。新增DatabaseManager.getAllPlatformConfigs()方法专门用于定时任务获取所有启用的平台配置。
- syncShelves云函数已完成重构：实现了基于game_account的去重机制、删除检测、性能优化（减少数据库查询、批量操作、性能监控），解决了重复账号数据和孤立数据问题
- shelf-monitor云函数数据同步问题修复：联动下架操作成功后必须立即更新本地数据库状态。在handleOffShelfOtherPlatforms函数中，调用adapter.offShelf()成功后需要立即调用updateShelfStatus()更新本地货架状态为OFFLINE(-1)，确保数据库状态实时同步。
